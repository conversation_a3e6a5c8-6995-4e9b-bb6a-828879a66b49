<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
<div id="root"></div>

    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
<script type="text/babel">
 function HomePage() {
    const [likes, setLikes] = React.useState(0);
  // 	...
  function handleClick() {
    console.log("点击前 likes =", likes);

    setLikes(likes + 1);
    setLikes(likes + 1);

    console.log("点击后 likes =", likes);

    console.log('increment like count');
  }
 
  return (
    <div>
      {/* ... */}
      <button onClick={handleClick}>Like({likes})</button>
    </div>
  );
}

ReactDOM.render(<HomePage />,
document.getElementById('root'))
</script>
 
</body>
</html>