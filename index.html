<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
<div id="root"></div>

    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
<script type="text/babel">
    function Header({ title }) {
  return <h1>{title ? title : 'Default title'}</h1>;
}
    function HomePage() {
  return (
    <div>
      <Header title="React" />
    </div>
  );
}

ReactDOM.render(<HomePage />,
document.getElementById('root'))
</script>
 
</body>
</html>