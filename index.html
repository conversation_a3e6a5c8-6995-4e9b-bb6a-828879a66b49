<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
<div id="root"></div>

    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
<script type="text/babel">
    function Header({ title },{ description }) {
        // console.log('props内容：', props);
        // console.log('props属性：', Object.keys(props));
        // console.log('props数量：', Object.keys(props).length);
        // console.log(props);
        // console.log('props title：', title);
    return <h1>{`${title} and ${description}`}</h1>;
}
 
// function HomePage() {
//   return (
//     <div>
//       <Header title="react " />
//       <Header title="vue" />
//       <Header description="angular" />
//     </div>
//   );
// }
// function Header({ title }) {
//   return <h1>{title ? title : 'Default title'}</h1>;
// }
 
// function HomePage() {
//   return (
//     <div>
//       <Header title="Hello World" />
//     </div>
//   );
// }
function HomePage() {
  const names = ['Ada Lovelace', 'Grace Hopper', 'Margaret Hamilton'];
 
  return (
    <div>
      <Header title="Develop. Preview. Ship." description="react" />
      <ul>
        {names.map((name) => (
          <li key={name}>{name}</li>
        ))}
      </ul>
    </div>
  );
}
ReactDOM.render(<HomePage />,
document.getElementById('root'))
</script>
 
</body>
</html>